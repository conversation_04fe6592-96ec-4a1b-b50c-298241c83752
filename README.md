# Forever Fest 2026

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://vercel.com/sean-bloniens-projects/forever-fest-2026)
[![Built with v0](https://img.shields.io/badge/Built%20with-v0.dev-black?style=for-the-badge)](https://v0.dev)

Sean & Eva's wedding website for Forever Fest 2026, featuring RSVP functionality, travel information, our story, and more.

## Tech Stack

- Next.js 15
- React 19
- TypeScript
- Radix UI for accessible components
- Tailwind CSS
- Framer Motion for animations
- ESLint
- Vercel Analytics & Speed Insights
- Deployed on Vercel

## Development

```sh
# Install dependencies
pnpm install

# Run development server
pnpm dev

# Run linter with auto-fix
pnpm lint
```
